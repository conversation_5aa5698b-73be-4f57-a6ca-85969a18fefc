const fs = require('fs');

// Read the testdriver04.ts file
const content = fs.readFileSync('testdriver04.ts', 'utf8');

// Extract questions manually by finding patterns
const questions = [];

// Find all question numbers
const numberMatches = [...content.matchAll(/numbers:\s*"ข้อที่\s*(\d+)\s*:/g)];

for (let i = 0; i < numberMatches.length; i++) {
    const questionNum = parseInt(numberMatches[i][1]);
    const startPos = numberMatches[i].index;
    const endPos = i < numberMatches.length - 1 ? numberMatches[i + 1].index : content.length;
    
    const questionBlock = content.substring(startPos, endPos);
    
    // Extract question text
    const textMatch = questionBlock.match(/{\s*text:\s*"([^"]+)"/);
    if (!textMatch) continue;
    
    const questionText = textMatch[1];
    
    // Check for image
    const hasImage = questionBlock.includes('image:');
    
    questions.push({
        number: questionNum,
        text: questionText,
        hasImage: hasImage
    });
}

// Categorize questions
function categorizeQuestion(question) {
    const text = question.text.toLowerCase();
    
    // Warning signs
    if ((text.includes('เครื่องหมายนี้') || text.includes('เครื่องหมาย')) && 
        (text.includes('หมายความว่า') || text.includes('ปฏิบัติอย่างไร')) && 
        question.hasImage) {
        return 'warning';
    }
    
    // Road markings
    if (text.includes('เขตปลอดภัย') || text.includes('แบ่งช่อง') || text.includes('บนพื้นทาง')) {
        return 'markings';
    }
    
    // Traffic law with images
    if (question.hasImage && (text.includes('จากภาพ') || text.includes('ภาพใด'))) {
        return 'trafficlawimage';
    }
    
    // Hazard perception
    if (text.includes('คาดการณ์') || text.includes('ระวัง') || text.includes('อุบัติเหตุ') || 
        text.includes('อันตราย') || text.includes('ปลอดภัย') || text.includes('บาดเจ็บ')) {
        return 'hazardperception';
    }
    
    // Vehicle law
    if (text.includes('ใบอนุญาต') || text.includes('ใบแทน') || text.includes('นายทะเบียน') || 
        text.includes('เอกสาร') || text.includes('ภาษี') || text.includes('ตรวจสภาพ') ||
        text.includes('เปลี่ยนสี') || text.includes('หมวกนิรภัย')) {
        return 'vehicleslaw';
    }
    
    // Traffic law
    if (text.includes('แอลกอฮอล์') || text.includes('หลบหนี') || text.includes('กลับรถ') || 
        text.includes('แซง') || text.includes('บรรทุก') || text.includes('เสียเงินเพิ่ม') ||
        text.includes('ผิด') || text.includes('โทษ') || text.includes('สุรา') ||
        text.includes('ลมหายใจ')) {
        return 'trafficlaw';
    }
    
    // Traffic signals
    if (text.includes('สัญญาณ') || text.includes('ไฟเลี้ยว') || text.includes('แตร') || 
        text.includes('เจ้าพนักงานจราจร') || text.includes('พนักงานจราจร') ||
        text.includes('เหยียดแขน')) {
        return 'traffic';
    }
    
    // Maintenance
    if (text.includes('น้ำในถัง') || text.includes('หม้อน้ำ') || text.includes('ระบบไฟฟ้า') || 
        text.includes('ตรวจสอบ') || text.includes('เติมน้ำ')) {
        return 'maintenance';
    }
    
    // Safe driving
    if (text.includes('ที่นั่ง') || text.includes('เกียร์') || text.includes('เบรก') || 
        text.includes('จอดรถ') || text.includes('ล้อ') || text.includes('เสียหลัก') ||
        text.includes('ลื่นไถล') || text.includes('ขับรถสวน') || text.includes('ระดับ') ||
        text.includes('ถอยหลัง') || text.includes('ทรงตัว')) {
        return 'safedriving';
    }
    
    // Awareness
    if (text.includes('มารยาท') || text.includes('ควรคำนึง') || text.includes('ผู้บาดเจ็บ') ||
        text.includes('หน้าที่')) {
        return 'awareness';
    }
    
    return 'unknown';
}

// Analyze questions
const analysis = {};
const categoryNames = {
    'warning': 'ป้ายเตือน (warning.ts)',
    'markings': 'เครื่องหมายบนถนน (markings.ts)',
    'trafficlawimage': 'กฎหมายจราจรพร้อมภาพ (trafficlawimage.ts)',
    'hazardperception': 'การรับรู้อันตราย (hazardperception1.ts / hazardperception2.ts)',
    'vehicleslaw': 'กฎหมายรถยนต์ (hvehicleslaw.ts)',
    'trafficlaw': 'กฎหมายจราจร (trafficlaw.ts)',
    'traffic': 'สัญญาณจราจร (traffic.ts)',
    'maintenance': 'การบำรุงรักษา (maintenance.ts)',
    'safedriving': 'การขับรถปลอดภัย (safedriving.ts)',
    'awareness': 'จิตสำนึก (awareness.ts)',
    'unknown': 'ไม่ระบุ'
};

questions.forEach(question => {
    const category = categorizeQuestion(question);
    if (!analysis[category]) {
        analysis[category] = [];
    }
    analysis[category].push(question);
});

console.log('='.repeat(80));
console.log('📊 การวิเคราะห์คำถามใน testdriver04.ts');
console.log('='.repeat(80));
console.log(`📝 จำนวนคำถามทั้งหมด: ${questions.length} ข้อ`);
console.log(`🖼️  คำถามที่มีรูปภาพ: ${questions.filter(q => q.hasImage).length} ข้อ`);
console.log(`📁 จำนวนหมวดหมู่: ${Object.keys(analysis).length} หมวด`);
console.log('');

// Display analysis
Object.keys(analysis).sort().forEach(category => {
    const categoryQuestions = analysis[category];
    const categoryName = categoryNames[category] || category;
    const imageCount = categoryQuestions.filter(q => q.hasImage).length;
    
    console.log(`📂 ${categoryName}`);
    console.log(`   - จำนวนข้อ: ${categoryQuestions.length} ข้อ`);
    console.log(`   - มีรูปภาพ: ${imageCount} ข้อ`);
    console.log(`   - ข้อที่: ${categoryQuestions.map(q => q.number).sort((a,b) => a-b).join(', ')}`);
    console.log('');
});

// Generate HTML
let html = `<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>การวิเคราะห์คำถามใน testdriver04.ts</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6; color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container {
            max-width: 1200px; margin: 0 auto; background: white;
            border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white; padding: 30px; text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .summary {
            background: #f8f9fa; padding: 30px;
            border-bottom: 1px solid #e9ecef;
        }
        .stats-grid {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px; margin-top: 20px;
        }
        .stat-card {
            background: white; padding: 20px; border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1); text-align: center;
            border-left: 4px solid #3498db;
        }
        .stat-number { font-size: 2.5em; font-weight: bold; color: #3498db; }
        .category-section {
            margin: 20px; background: #f8f9fa; border-radius: 10px;
            overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        .category-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white; padding: 20px; font-size: 1.3em; font-weight: bold;
        }
        .question-grid {
            display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px; padding: 20px;
        }
        .question-card {
            background: white; border: 1px solid #e9ecef; border-radius: 8px;
            padding: 15px; transition: all 0.3s ease;
        }
        .question-card:hover { transform: translateY(-2px); }
        .question-number {
            background: #3498db; color: white; padding: 5px 10px;
            border-radius: 15px; font-size: 0.8em; font-weight: bold;
            display: inline-block; margin-bottom: 10px;
        }
        .has-image { border-left: 4px solid #e74c3c; }
        .image-indicator {
            background: #e74c3c; color: white; padding: 2px 8px;
            border-radius: 10px; font-size: 0.7em; margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 การวิเคราะห์คำถามใน testdriver04.ts</h1>
            <p>แยกประเภทคำถามตามไฟล์ต้นฉบับ</p>
        </div>
        <div class="summary">
            <h2>📊 สรุปภาพรวม</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${questions.length}</div>
                    <div>คำถามทั้งหมด</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${questions.filter(q => q.hasImage).length}</div>
                    <div>คำถามที่มีรูปภาพ</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${Object.keys(analysis).length}</div>
                    <div>หมวดหมู่</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${Math.round((questions.filter(q => q.hasImage).length / questions.length) * 100)}%</div>
                    <div>สัดส่วนรูปภาพ</div>
                </div>
            </div>
        </div>`;

// Add categories
Object.keys(analysis).sort().forEach(category => {
    const categoryQuestions = analysis[category];
    const categoryName = categoryNames[category] || category;
    
    html += `
        <div class="category-section">
            <div class="category-header">
                📁 ${categoryName} (${categoryQuestions.length} ข้อ)
            </div>
            <div class="question-grid">`;
    
    categoryQuestions.forEach(question => {
        html += `
                <div class="question-card ${question.hasImage ? 'has-image' : ''}">
                    <div class="question-number">
                        ข้อ ${question.number}
                        ${question.hasImage ? '<span class="image-indicator">📷</span>' : ''}
                    </div>
                    <div>${question.text}</div>
                </div>`;
    });
    
    html += `</div></div>`;
});

html += `
    </div>
</body>
</html>`;

fs.writeFileSync('testdriver04_analysis.html', html);
console.log('✅ สร้างไฟล์ testdriver04_analysis.html เรียบร้อยแล้ว');
