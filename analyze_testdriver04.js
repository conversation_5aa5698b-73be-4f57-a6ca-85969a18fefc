const fs = require('fs');

// Read the testdriver04.ts file
const testDriverContent = fs.readFileSync('testdriver04.ts', 'utf8');

// Extract questions from the file using a simpler approach
const questions = [];

// Split by question blocks
const questionBlocks = testDriverContent.split('numbers: "ข้อที่').slice(1);

questionBlocks.forEach(block => {
    // Extract question number
    const numberMatch = block.match(/^(\d+)\s*:/);
    if (!numberMatch) return;

    const questionNum = parseInt(numberMatch[1]);

    // Extract question text
    const textMatch = block.match(/text:\s*"([^"]+)"/);
    if (!textMatch) return;

    const questionText = textMatch[1];

    // Check for image
    const imageMatch = block.match(/image:\s*"([^"]+)"/);
    const imageUrl = imageMatch ? imageMatch[1] : null;

    questions.push({
        number: questionNum,
        text: questionText,
        image: imageUrl,
        hasImage: !!imageUrl
    });
});

// Categorize questions based on content and characteristics
function categorizeQuestion(question) {
    const text = question.text.toLowerCase();
    
    // Warning signs (ป้ายเตือน)
    if (text.includes('เครื่องหมายนี้') && question.hasImage && 
        (text.includes('หมายความว่า') || text.includes('ปฏิบัติอย่างไร'))) {
        return 'warning';
    }
    
    // Mandatory signs (ป้ายบังคับ)
    if (text.includes('เครื่องหมายนี้') && question.hasImage && text.includes('ปฏิบัติ')) {
        return 'mandatorysign';
    }
    
    // Road markings (เครื่องหมายบนถนน)
    if (text.includes('เครื่องหมาย') && (text.includes('แบ่งช่อง') || text.includes('บนพื้นทาง') || text.includes('เขตปลอดภัย'))) {
        return 'markings';
    }
    
    // Traffic law with images (กฎหมายจราจรพร้อมภาพ)
    if (question.hasImage && (text.includes('จากภาพ') || text.includes('ภาพใด'))) {
        return 'trafficlawimage';
    }
    
    // Hazard perception (การรับรู้อันตราย)
    if (text.includes('คาดการณ์') || text.includes('ระวัง') || text.includes('อุบัติเหตุ') || 
        text.includes('อันตราย') || text.includes('ปลอดภัย')) {
        return 'hazardperception';
    }
    
    // Vehicle law (กฎหมายรถยนต์)
    if (text.includes('ใบอนุญาต') || text.includes('ใบแทน') || text.includes('นายทะเบียน') || 
        text.includes('เอกสาร') || text.includes('ภาษี') || text.includes('ตรวจสภาพ')) {
        return 'vehicleslaw';
    }
    
    // Traffic law (กฎหมายจราจร)
    if (text.includes('แอลกอฮอล์') || text.includes('หลบหนี') || text.includes('กลับรถ') || 
        text.includes('แซง') || text.includes('บรรทุก') || text.includes('หมวกนิรภัย') ||
        text.includes('เสียเงินเพิ่ม') || text.includes('ผิด') || text.includes('โทษ')) {
        return 'trafficlaw';
    }
    
    // Traffic signals (สัญญาณจราจร)
    if (text.includes('สัญญาณ') || text.includes('ไฟเลี้ยว') || text.includes('แตร') || 
        text.includes('เจ้าพนักงานจราจร') || text.includes('พนักงานจราจร')) {
        return 'traffic';
    }
    
    // Maintenance (การบำรุงรักษา)
    if (text.includes('น้ำในถัง') || text.includes('หม้อน้ำ') || text.includes('ระบบไฟฟ้า') || 
        text.includes('ตรวจสอบ') || text.includes('บำรุง')) {
        return 'maintenance';
    }
    
    // Safe driving (การขับรขปลอดภัย)
    if (text.includes('ที่นั่ง') || text.includes('เกียร์') || text.includes('เบรก') || 
        text.includes('จอดรถ') || text.includes('ล้อ') || text.includes('เสียหลัก') ||
        text.includes('ลื่นไถล') || text.includes('ขับรถสวน')) {
        return 'safedriving';
    }
    
    // Awareness (จิตสำนึก)
    if (text.includes('มารยาท') || text.includes('ควรคำนึง') || text.includes('ผู้บาดเจ็บ')) {
        return 'awareness';
    }
    
    return 'unknown';
}

// Analyze all questions
const analysis = {};
const categoryNames = {
    'warning': 'ป้ายเตือน (Warning Signs)',
    'mandatorysign': 'ป้ายบังคับ (Mandatory Signs)', 
    'markings': 'เครื่องหมายบนถนน (Road Markings)',
    'trafficlawimage': 'กฎหมายจราจรพร้อมภาพ (Traffic Law with Images)',
    'hazardperception': 'การรับรู้อันตราย (Hazard Perception)',
    'vehicleslaw': 'กฎหมายรถยนต์ (Vehicle Law)',
    'trafficlaw': 'กฎหมายจราจร (Traffic Law)',
    'traffic': 'สัญญาณจราจร (Traffic Signals)',
    'maintenance': 'การบำรุงรักษา (Maintenance)',
    'safedriving': 'การขับรถปลอดภัย (Safe Driving)',
    'awareness': 'จิตสำนึก (Awareness)',
    'unknown': 'ไม่ระบุ (Unknown)'
};

questions.forEach(question => {
    const category = categorizeQuestion(question);
    if (!analysis[category]) {
        analysis[category] = [];
    }
    analysis[category].push(question);
});

// Generate HTML report
let html = `<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>การวิเคราะห์คำถามใน testdriver04.ts</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .summary {
            background: #f8f9fa;
            padding: 30px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .summary h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #3498db;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .content {
            padding: 30px;
        }
        
        .category-section {
            margin-bottom: 40px;
            background: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .category-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .category-content {
            padding: 20px;
        }
        
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .question-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        
        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .question-number {
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .question-text {
            color: #2c3e50;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .has-image {
            border-left: 4px solid #e74c3c;
        }
        
        .image-indicator {
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7em;
            margin-left: 10px;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .question-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 การวิเคราะห์คำถามใน testdriver04.ts</h1>
            <p>แยกประเภทคำถามตามไฟล์ต้นฉบับ</p>
        </div>
        
        <div class="summary">
            <h2>📊 สรุปภาพรวม</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${questions.length}</div>
                    <div class="stat-label">คำถามทั้งหมด</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${questions.filter(q => q.hasImage).length}</div>
                    <div class="stat-label">คำถามที่มีรูปภาพ</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${Object.keys(analysis).length}</div>
                    <div class="stat-label">หมวดหมู่</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${Math.round((questions.filter(q => q.hasImage).length / questions.length) * 100)}%</div>
                    <div class="stat-label">สัดส่วนรูปภาพ</div>
                </div>
            </div>
        </div>
        
        <div class="content">`;

// Add each category section
Object.keys(analysis).sort().forEach(category => {
    const categoryQuestions = analysis[category];
    const categoryName = categoryNames[category] || category;
    
    html += `
            <div class="category-section">
                <div class="category-header">
                    📁 ${categoryName} (${categoryQuestions.length} ข้อ)
                </div>
                <div class="category-content">
                    <div class="question-grid">`;
    
    categoryQuestions.forEach(question => {
        html += `
                        <div class="question-card ${question.hasImage ? 'has-image' : ''}">
                            <div class="question-number">
                                ข้อ ${question.number}
                                ${question.hasImage ? '<span class="image-indicator">📷</span>' : ''}
                            </div>
                            <div class="question-text">${question.text}</div>
                        </div>`;
    });
    
    html += `
                    </div>
                </div>
            </div>`;
});

html += `
        </div>
        
        <div class="footer">
            <p>🔍 วิเคราะห์โดยระบบอัตโนมัติ | สร้างเมื่อ ${new Date().toLocaleString('th-TH')}</p>
        </div>
    </div>
</body>
</html>`;

// Write HTML file
fs.writeFileSync('testdriver04_analysis.html', html);

// Generate summary report
console.log('='.repeat(60));
console.log('📊 สรุปการวิเคราะห์คำถามใน testdriver04.ts');
console.log('='.repeat(60));
console.log(`📝 จำนวนคำถามทั้งหมด: ${questions.length} ข้อ`);
console.log(`🖼️  คำถามที่มีรูปภาพ: ${questions.filter(q => q.hasImage).length} ข้อ`);
console.log(`📁 จำนวนหมวดหมู่: ${Object.keys(analysis).length} หมวด`);
console.log('');

Object.keys(analysis).sort().forEach(category => {
    const categoryQuestions = analysis[category];
    const categoryName = categoryNames[category] || category;
    const imageCount = categoryQuestions.filter(q => q.hasImage).length;
    
    console.log(`📂 ${categoryName}`);
    console.log(`   - จำนวนข้อ: ${categoryQuestions.length} ข้อ`);
    console.log(`   - มีรูปภาพ: ${imageCount} ข้อ`);
    console.log(`   - ข้อที่: ${categoryQuestions.map(q => q.number).join(', ')}`);
    console.log('');
});

console.log('✅ สร้างไฟล์ testdriver04_analysis.html เรียบร้อยแล้ว');
