#!/usr/bin/env python3
import re
import json
from bs4 import BeautifulSoup

def extract_questions_from_html(html_file):
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    questions = []
    
    # Find all question containers
    question_containers = soup.find_all('div', class_='OxAavc')
    
    for container in question_containers:
        # Skip email field
        if 'อีเมล' in container.get_text():
            continue
            
        # Find question text
        question_span = container.find('span', class_='M7eMe')
        if not question_span:
            continue
            
        question_text = question_span.get_text().strip()
        
        # Extract question number
        question_match = re.match(r'(\d+)\.\s*(.*)', question_text)
        if not question_match:
            continue
            
        question_num = question_match.group(1)
        question_content = question_match.group(2)
        
        # Find image if exists
        img_tag = container.find('img')
        image_url = img_tag['src'] if img_tag else None
        
        # Find options
        options = []
        option_labels = container.find_all('label')
        
        for label in option_labels:
            option_span = label.find('span', class_='aDTYNe')
            if option_span:
                option_text = option_span.get_text().strip()
                if option_text and not option_text.startswith('คำตอบที่ถูกต้อง'):
                    options.append(option_text)
        
        # Find correct answer
        correct_answer_container = container.find('div', class_='D42QGf')
        correct_answer_index = 0
        
        if correct_answer_container:
            correct_label = correct_answer_container.find('label')
            if correct_label:
                correct_span = correct_label.find('span', class_='aDTYNe')
                if correct_span:
                    correct_text = correct_span.get_text().strip()
                    for i, option in enumerate(options):
                        if correct_text in option or option in correct_text:
                            correct_answer_index = i
                            break
        
        # Build question object
        question_obj = {
            'numbers': f'ข้อที่ {question_num} :',
            'question': [{'text': question_content}],
            'options': [{'text': opt} for opt in options],
            'correctAnswer': correct_answer_index,
            'order': ['text']
        }
        
        # Add image if exists
        if image_url:
            question_obj['question'].append({'image': image_url})
            question_obj['order'] = ['text', 'image']
        
        questions.append(question_obj)
    
    return questions

def generate_typescript_file(questions, output_file):
    ts_content = '''export type Option = {
  text?: string;
  image?: string;
};

export type Question = {
  numbers: string;
  question: Option[];
  options: Option[]; // options can be either text or an image object
  correctAnswer: number; // Index of the correct answer
  order: ("text" | "image")[];
};

export const quizData: Question[] = [
'''
    
    for i, question in enumerate(questions):
        ts_content += '  {\n'
        ts_content += f'    numbers: "{question["numbers"]}",\n'
        ts_content += '    question: [\n'
        for q in question['question']:
            if 'text' in q:
                ts_content += f'      {{ text: "{q["text"]}" }},\n'
            elif 'image' in q:
                ts_content += f'      {{ image: "{q["image"]}" }},\n'
        ts_content += '    ],\n'
        ts_content += '    options: [\n'
        for opt in question['options']:
            ts_content += f'      {{ text: "{opt["text"]}" }},\n'
        ts_content += '    ],\n'
        ts_content += f'    correctAnswer: {question["correctAnswer"]},\n'
        ts_content += f'    order: {json.dumps(question["order"])},\n'
        ts_content += '  }'
        if i < len(questions) - 1:
            ts_content += ','
        ts_content += '\n'
    
    ts_content += '];\n'
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(ts_content)

if __name__ == '__main__':
    questions = extract_questions_from_html('testdriver.html')
    print(f"Extracted {len(questions)} questions")
    generate_typescript_file(questions, 'testdriver_full.ts')
    print("Generated testdriver_full.ts")
